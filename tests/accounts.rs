mod common;

use shredstream_decoder::types::{MessageAddressTableLookup, Pubkey};
use solana_pubkey::Pubkey as SolanaPubkey;

use common::{assertions::*, fixtures::*, mock_data::generators::*, reference_implementations::reference::*};

mod pubkey_tests {
    use super::*;

    #[test]
    fn test_pubkey_binary_representation() {
        let test_cases = vec![
            [0u8; 32],
            [255u8; 32],
            [
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
                29, 30, 31, 32,
            ],
        ];

        for test_array in test_cases {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
            assert_eq!(custom_pubkey.to_bytes(), test_array);
            assert_eq!(reference_pubkey.to_bytes(), test_array);

            assert_pubkey_compatible!(custom_pubkey, reference_pubkey);
        }
    }

    #[test]
    fn test_pubkey_serialization_compatibility() {
        let test_arrays = vec![[0u8; 32], [255u8; 32], [42u8; 32], [128u8; 32]];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
            let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();

            assert_binary_compatible!(custom_serialized, reference_serialized);
            assert_serialization_compatible!(custom_pubkey, reference_pubkey);
        }
    }

    #[test]
    fn test_pubkey_deserialization_compatibility() {
        let test_arrays = vec![[0u8; 32], [255u8; 32], [42u8; 32]];

        for test_array in test_arrays {
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);
            let reference_bytes = serialize_pubkey_reference(&reference_pubkey).unwrap();

            let custom_result: Result<Pubkey, _> = bincode::deserialize(&reference_bytes);
            let reference_result = deserialize_pubkey_reference(&reference_bytes);

            match (custom_result, reference_result) {
                (Ok(custom), Ok(reference)) => {
                    assert_pubkey_compatible!(custom, reference);
                    assert_eq!(custom.to_bytes(), reference.to_bytes());
                }
                (Err(_), Err(_)) => {}
                _ => panic!("Deserialization compatibility failed: one succeeded, one failed"),
            }
        }
    }

    #[test]
    fn test_pubkey_from_array_compatibility() {
        let test_arrays = vec![
            [0u8; 32],
            [1u8; 32],
            [255u8; 32],
            [
                42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67,
                68, 69, 70, 71, 72, 73,
            ],
        ];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::from(test_array);
            let reference_pubkey = SolanaPubkey::from(test_array);

            assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
            assert_eq!(custom_pubkey.to_bytes(), reference_pubkey.to_bytes());

            let custom_pubkey2 = Pubkey::new_from_array(test_array);
            assert_eq!(custom_pubkey.as_ref(), custom_pubkey2.as_ref());
        }
    }

    #[test]
    fn test_pubkey_to_bytes_compatibility() {
        let test_arrays = vec![[0u8; 32], [255u8; 32], [128u8; 32]];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            let custom_bytes = custom_pubkey.to_bytes();
            let reference_bytes = reference_pubkey.to_bytes();

            assert_eq!(custom_bytes, reference_bytes);
            assert_eq!(custom_bytes, test_array);
            assert_eq!(reference_bytes, test_array);
        }
    }

    #[test]
    fn test_pubkey_as_ref_compatibility() {
        let test_arrays = vec![
            [0u8; 32],
            [255u8; 32],
            [
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
                29, 30, 31, 32,
            ],
        ];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            let custom_ref: &[u8] = custom_pubkey.as_ref();
            let reference_ref: &[u8] = reference_pubkey.as_ref();

            assert_eq!(custom_ref, reference_ref);
            assert_eq!(custom_ref, &test_array[..]);
            assert_eq!(reference_ref, &test_array[..]);
            assert_eq!(custom_ref.len(), 32);
            assert_eq!(reference_ref.len(), 32);
        }
    }

    #[test]
    fn test_pubkey_zero_bytes() {
        let zero_array = [0u8; 32];
        let custom_pubkey = Pubkey::new_from_array(zero_array);
        let reference_pubkey = SolanaPubkey::new_from_array(zero_array);

        assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
        assert_eq!(custom_pubkey.to_bytes(), zero_array);
        assert_eq!(reference_pubkey.to_bytes(), zero_array);

        let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
        let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
        assert_binary_compatible!(custom_serialized, reference_serialized);

        assert_roundtrip_compatible!(custom_pubkey);
    }

    #[test]
    fn test_pubkey_max_bytes() {
        let max_array = [255u8; 32];
        let custom_pubkey = Pubkey::new_from_array(max_array);
        let reference_pubkey = SolanaPubkey::new_from_array(max_array);

        assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
        assert_eq!(custom_pubkey.to_bytes(), max_array);
        assert_eq!(reference_pubkey.to_bytes(), max_array);

        let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
        let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
        assert_binary_compatible!(custom_serialized, reference_serialized);

        assert_roundtrip_compatible!(custom_pubkey);
    }

    #[test]
    fn test_pubkey_random_bytes() {
        let test_seeds = vec![12345u64, 67890, 999999, 1, u64::MAX];

        for seed in test_seeds {
            let custom_pubkey = generate_mock_pubkey_with_seed(seed);
            let test_array = custom_pubkey.to_bytes();
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
            assert_eq!(custom_pubkey.to_bytes(), reference_pubkey.to_bytes());

            let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
            let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
            assert_binary_compatible!(custom_serialized, reference_serialized);

            assert_roundtrip_compatible!(custom_pubkey);
        }
    }

    #[test]
    fn test_pubkey_default_compatibility() {
        let custom_default = Pubkey::default();
        let reference_default = SolanaPubkey::default();

        assert_eq!(custom_default.as_ref(), reference_default.as_ref());
        assert_eq!(custom_default.to_bytes(), reference_default.to_bytes());
        assert_eq!(custom_default.to_bytes(), [0u8; 32]);

        let custom_serialized = bincode::serialize(&custom_default).unwrap();
        let reference_serialized = serialize_pubkey_reference(&reference_default).unwrap();
        assert_binary_compatible!(custom_serialized, reference_serialized);
    }

    #[test]
    fn test_pubkey_clone_and_copy() {
        let test_array = [42u8; 32];
        let original = Pubkey::new_from_array(test_array);

        let cloned = original.clone();
        let copied = original;

        assert_eq!(original.as_ref(), cloned.as_ref());
        assert_eq!(original.as_ref(), copied.as_ref());
        assert_eq!(cloned.as_ref(), copied.as_ref());

        assert_eq!(original.to_bytes(), test_array);
        assert_eq!(cloned.to_bytes(), test_array);
        assert_eq!(copied.to_bytes(), test_array);
    }

    #[test]
    fn test_pubkey_equality_and_ordering() {
        let array1 = [1u8; 32];
        let array2 = [2u8; 32];
        let array1_copy = [1u8; 32];

        let pubkey1 = Pubkey::new_from_array(array1);
        let pubkey2 = Pubkey::new_from_array(array2);
        let pubkey1_copy = Pubkey::new_from_array(array1_copy);

        assert_eq!(pubkey1, pubkey1_copy);
        assert_ne!(pubkey1, pubkey2);
        assert!(pubkey1 < pubkey2);
        assert!(pubkey1 <= pubkey1_copy);
        assert!(pubkey2 > pubkey1);
    }

    #[test]
    fn test_pubkey_hash_compatibility() {
        use std::collections::HashMap;

        let test_array = [123u8; 32];
        let custom_pubkey = Pubkey::new_from_array(test_array);

        let mut map = HashMap::new();
        map.insert(custom_pubkey, "test_value");

        let lookup_key = Pubkey::new_from_array(test_array);
        assert_eq!(map.get(&lookup_key), Some(&"test_value"));

        let different_key = Pubkey::new_from_array([124u8; 32]);
        assert_eq!(map.get(&different_key), None);
    }

    #[test]
    fn test_pubkey_comprehensive_roundtrip() {
        let test_cases = vec![
            [0u8; 32],
            [255u8; 32],
            [128u8; 32],
            generate_mock_pubkey_with_seed(12345).to_bytes(),
            generate_mock_pubkey_with_seed(67890).to_bytes(),
        ];

        for test_array in test_cases {
            let original_pubkey = Pubkey::new_from_array(test_array);

            let serialized = bincode::serialize(&original_pubkey).unwrap();
            let deserialized: Pubkey = bincode::deserialize(&serialized).unwrap();

            assert_eq!(original_pubkey, deserialized);
            assert_eq!(original_pubkey.as_ref(), deserialized.as_ref());
            assert_eq!(original_pubkey.to_bytes(), deserialized.to_bytes());

            let re_serialized = bincode::serialize(&deserialized).unwrap();
            assert_eq!(serialized, re_serialized);
        }
    }
}

mod message_address_table_lookup_tests {
    use super::*;

    #[test]
    fn test_address_table_lookup_basic_structure() {
        let account_key = Pubkey::new_from_array([42u8; 32]);
        let writable_indexes = vec![0, 1, 2];
        let readonly_indexes = vec![3, 4, 5];

        let lookup = MessageAddressTableLookup {
            account_key,
            writable_indexes: writable_indexes.clone(),
            readonly_indexes: readonly_indexes.clone(),
        };

        assert_eq!(lookup.account_key, account_key);
        assert_eq!(lookup.writable_indexes, writable_indexes);
        assert_eq!(lookup.readonly_indexes, readonly_indexes);
    }

    #[test]
    fn test_address_table_lookup_default() {
        let lookup = MessageAddressTableLookup::default();

        assert_eq!(lookup.account_key, Pubkey::default());
        assert_eq!(lookup.writable_indexes, Vec::<u8>::new());
        assert_eq!(lookup.readonly_indexes, Vec::<u8>::new());
    }

    #[test]
    fn test_address_table_lookup_clone_and_equality() {
        let lookup1 = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([1u8; 32]),
            writable_indexes: vec![0, 1],
            readonly_indexes: vec![2, 3],
        };

        let lookup2 = lookup1.clone();
        assert_eq!(lookup1, lookup2);

        let lookup3 = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([1u8; 32]),
            writable_indexes: vec![0, 1],
            readonly_indexes: vec![2, 4], // Different readonly_indexes
        };
        assert_ne!(lookup1, lookup3);
    }

    #[test]
    fn test_address_table_lookup_serialization_roundtrip() {
        let original = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([123u8; 32]),
            writable_indexes: vec![0, 5, 10, 15],
            readonly_indexes: vec![20, 25, 30],
        };

        let serialized = bincode::serialize(&original).unwrap();
        let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();

        assert_eq!(original, deserialized);
        assert_eq!(original.account_key, deserialized.account_key);
        assert_eq!(original.writable_indexes, deserialized.writable_indexes);
        assert_eq!(original.readonly_indexes, deserialized.readonly_indexes);
    }

    #[test]
    fn test_address_table_lookup_empty_indexes() {
        let lookup = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([255u8; 32]),
            writable_indexes: vec![],
            readonly_indexes: vec![],
        };

        let serialized = bincode::serialize(&lookup).unwrap();
        let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();
        assert_eq!(lookup, deserialized);

        // Test with only one empty
        let lookup_partial = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([128u8; 32]),
            writable_indexes: vec![1, 2, 3],
            readonly_indexes: vec![],
        };

        let serialized = bincode::serialize(&lookup_partial).unwrap();
        let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();
        assert_eq!(lookup_partial, deserialized);
    }

    #[test]
    fn test_address_table_lookup_large_indexes() {
        // Test with maximum u8 values
        let lookup = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([200u8; 32]),
            writable_indexes: vec![0, 127, 255],
            readonly_indexes: vec![1, 128, 254],
        };

        let serialized = bincode::serialize(&lookup).unwrap();
        let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();
        assert_eq!(lookup, deserialized);

        // Verify max values are preserved
        assert_eq!(deserialized.writable_indexes[2], 255);
        assert_eq!(deserialized.readonly_indexes[2], 254);
    }

    #[test]
    fn test_address_table_lookup_with_mock_generators() {
        // Test with mock data generators
        let mock_lookup = generate_mock_address_table_lookup();

        // Verify structure is valid
        assert_ne!(mock_lookup.account_key, Pubkey::default());
        assert!(!mock_lookup.writable_indexes.is_empty());
        assert!(!mock_lookup.readonly_indexes.is_empty());

        // Test serialization of mock data
        let serialized = bincode::serialize(&mock_lookup).unwrap();
        let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();
        assert_eq!(mock_lookup, deserialized);
    }

    #[test]
    fn test_address_table_lookup_with_seed_generators() {
        let test_seeds = vec![1u64, 100, 1000, 12345, u64::MAX];

        for seed in test_seeds {
            let lookup = generate_mock_address_table_lookup_with_seed(seed);

            // Verify generated data is deterministic
            let lookup2 = generate_mock_address_table_lookup_with_seed(seed);
            assert_eq!(lookup, lookup2);

            // Test serialization roundtrip
            let serialized = bincode::serialize(&lookup).unwrap();
            let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();
            assert_eq!(lookup, deserialized);

            // Verify indexes are valid u8 values
            for &index in &lookup.writable_indexes {
                assert!(index <= 255);
            }
            for &index in &lookup.readonly_indexes {
                assert!(index <= 255);
            }
        }
    }

    #[test]
    fn test_address_table_lookup_writable_indexes_compatibility() {
        // Test various writable index scenarios
        let scenarios = vec![
            vec![],                   // Empty
            vec![0],                  // Single index
            vec![0, 1, 2, 3, 4],      // Sequential
            vec![255, 0, 128],        // Mixed values
            vec![10, 20, 30, 40, 50], // Spaced values
        ];

        for writable_indexes in scenarios {
            let lookup = MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([42u8; 32]),
                writable_indexes: writable_indexes.clone(),
                readonly_indexes: vec![100, 101],
            };

            let serialized = bincode::serialize(&lookup).unwrap();
            let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();

            assert_eq!(lookup.writable_indexes, writable_indexes);
            assert_eq!(deserialized.writable_indexes, writable_indexes);
            assert_eq!(lookup, deserialized);
        }
    }

    #[test]
    fn test_address_table_lookup_readonly_indexes_compatibility() {
        // Test various readonly index scenarios
        let scenarios = vec![
            vec![],              // Empty
            vec![255],           // Single max value
            vec![0, 1, 2],       // Sequential from 0
            vec![253, 254, 255], // Sequential high values
            vec![1, 3, 5, 7, 9], // Odd numbers
        ];

        for readonly_indexes in scenarios {
            let lookup = MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([99u8; 32]),
                writable_indexes: vec![50, 51],
                readonly_indexes: readonly_indexes.clone(),
            };

            let serialized = bincode::serialize(&lookup).unwrap();
            let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();

            assert_eq!(lookup.readonly_indexes, readonly_indexes);
            assert_eq!(deserialized.readonly_indexes, readonly_indexes);
            assert_eq!(lookup, deserialized);
        }
    }

    #[test]
    fn test_address_table_lookup_edge_cases() {
        // Test with all possible edge case combinations
        let edge_cases = vec![
            // (account_key_pattern, writable_indexes, readonly_indexes)
            ([0u8; 32], vec![], vec![]),               // All zeros, empty indexes
            ([255u8; 32], vec![255], vec![255]),       // All max values
            ([128u8; 32], vec![0, 255], vec![0, 255]), // Mixed boundary values
            ([42u8; 32], vec![1, 2, 3, 4, 5], vec![]), // Only writable
            ([99u8; 32], vec![], vec![10, 20, 30]),    // Only readonly
        ];

        for (account_pattern, writable_indexes, readonly_indexes) in edge_cases {
            let lookup = MessageAddressTableLookup {
                account_key: Pubkey::new_from_array(account_pattern),
                writable_indexes: writable_indexes.clone(),
                readonly_indexes: readonly_indexes.clone(),
            };

            // Test serialization roundtrip
            let serialized = bincode::serialize(&lookup).unwrap();
            let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();
            assert_eq!(lookup, deserialized);

            // Verify all fields are preserved
            assert_eq!(lookup.account_key.to_bytes(), account_pattern);
            assert_eq!(lookup.writable_indexes, writable_indexes);
            assert_eq!(lookup.readonly_indexes, readonly_indexes);
        }
    }

    #[test]
    fn test_address_table_lookup_short_vec_serialization() {
        // Test that short_vec serialization works correctly
        let lookup = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([77u8; 32]),
            writable_indexes: (0..10).collect(),  // 10 elements
            readonly_indexes: (10..25).collect(), // 15 elements
        };

        let serialized = bincode::serialize(&lookup).unwrap();
        let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();

        assert_eq!(lookup, deserialized);
        assert_eq!(lookup.writable_indexes.len(), 10);
        assert_eq!(lookup.readonly_indexes.len(), 15);
        assert_eq!(deserialized.writable_indexes.len(), 10);
        assert_eq!(deserialized.readonly_indexes.len(), 15);

        // Verify the actual values
        for (i, &value) in lookup.writable_indexes.iter().enumerate() {
            assert_eq!(value, i as u8);
        }
        for (i, &value) in lookup.readonly_indexes.iter().enumerate() {
            assert_eq!(value, (i + 10) as u8);
        }
    }

    #[test]
    fn test_address_table_lookup_large_arrays() {
        // Test with larger arrays to stress test short_vec
        let large_writable: Vec<u8> = (0..100).collect();
        let large_readonly: Vec<u8> = (100..200).collect();

        let lookup = MessageAddressTableLookup {
            account_key: Pubkey::new_from_array([200u8; 32]),
            writable_indexes: large_writable.clone(),
            readonly_indexes: large_readonly.clone(),
        };

        let serialized = bincode::serialize(&lookup).unwrap();
        let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();

        assert_eq!(lookup, deserialized);
        assert_eq!(lookup.writable_indexes, large_writable);
        assert_eq!(lookup.readonly_indexes, large_readonly);
        assert_eq!(deserialized.writable_indexes, large_writable);
        assert_eq!(deserialized.readonly_indexes, large_readonly);
    }

    #[test]
    fn test_address_table_lookup_comprehensive_roundtrip() {
        // Test comprehensive scenarios with different combinations
        let test_cases = vec![
            // Basic cases
            (Pubkey::default(), vec![], vec![]),
            (Pubkey::new_from_array([1u8; 32]), vec![0], vec![1]),
            // Generated cases
            (generate_mock_pubkey_with_seed(123), vec![1, 2, 3], vec![4, 5, 6]),
            (generate_mock_pubkey_with_seed(456), vec![255, 0, 128], vec![]),
            (generate_mock_pubkey_with_seed(789), vec![], vec![10, 20, 30, 40]),
            // Edge cases
            (Pubkey::new_from_array([255u8; 32]), vec![255], vec![255]),
            (Pubkey::new_from_array([128u8; 32]), (0..50).collect(), (50..100).collect()),
        ];

        for (account_key, writable_indexes, readonly_indexes) in test_cases {
            let original = MessageAddressTableLookup {
                account_key,
                writable_indexes: writable_indexes.clone(),
                readonly_indexes: readonly_indexes.clone(),
            };

            // First roundtrip
            let serialized = bincode::serialize(&original).unwrap();
            let deserialized: MessageAddressTableLookup = bincode::deserialize(&serialized).unwrap();
            assert_eq!(original, deserialized);

            // Second roundtrip to ensure stability
            let re_serialized = bincode::serialize(&deserialized).unwrap();
            let re_deserialized: MessageAddressTableLookup = bincode::deserialize(&re_serialized).unwrap();
            assert_eq!(original, re_deserialized);
            assert_eq!(serialized, re_serialized);

            // Verify individual fields
            assert_eq!(original.account_key, re_deserialized.account_key);
            assert_eq!(original.writable_indexes, re_deserialized.writable_indexes);
            assert_eq!(original.readonly_indexes, re_deserialized.readonly_indexes);
        }
    }
}
