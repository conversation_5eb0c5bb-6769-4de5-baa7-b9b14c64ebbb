use shredstream_decoder::decode_entries_internal;
use shredstream_decoder::types::Entry as CustomEntry;
use solana_entry::entry::Entry as SolanaEntry;
use std::fs;
use std::path::Path;

/// Deep comparison function to verify 100% data accuracy
fn compare_entries_deeply(
    custom_entries: &[CustomEntry],
    reference_entries: &[SolanaEntry],
    filename: &str,
) -> Option<String> {
    // First check: entry count
    if custom_entries.len() != reference_entries.len() {
        return Some(format!(
            "{}: Entry count mismatch - Custom: {}, Reference: {}",
            filename,
            custom_entries.len(),
            reference_entries.len()
        ));
    }

    // Deep comparison for each entry
    for (i, (custom_entry, ref_entry)) in custom_entries.iter().zip(reference_entries.iter()).enumerate() {
        // Compare num_hashes
        if custom_entry.num_hashes != ref_entry.num_hashes {
            return Some(format!(
                "{}: Entry[{}] num_hashes mismatch - Custom: {}, Reference: {}",
                filename, i, custom_entry.num_hashes, ref_entry.num_hashes
            ));
        }

        // Compare hash bytes
        if custom_entry.hash.to_bytes() != ref_entry.hash.to_bytes() {
            return Some(format!(
                "{}: Entry[{}] hash mismatch - Custom: {:?}, Reference: {:?}",
                filename,
                i,
                custom_entry.hash.to_bytes(),
                ref_entry.hash.to_bytes()
            ));
        }

        // Compare transaction count
        if custom_entry.transactions.len() != ref_entry.transactions.len() {
            return Some(format!(
                "{}: Entry[{}] transaction count mismatch - Custom: {}, Reference: {}",
                filename,
                i,
                custom_entry.transactions.len(),
                ref_entry.transactions.len()
            ));
        }

        // Compare each transaction by serializing and comparing bytes
        // This is the most reliable way to ensure 100% accuracy
        for (j, (custom_tx, ref_tx)) in custom_entry.transactions.iter().zip(ref_entry.transactions.iter()).enumerate()
        {
            // Serialize both transactions to bytes for comparison
            let custom_tx_bytes = match bincode::serialize(custom_tx) {
                Ok(bytes) => bytes,
                Err(e) => {
                    return Some(format!(
                        "{}: Entry[{}] Transaction[{}] - Failed to serialize custom transaction: {}",
                        filename, i, j, e
                    ))
                }
            };

            let ref_tx_bytes = match bincode::serialize(ref_tx) {
                Ok(bytes) => bytes,
                Err(e) => {
                    return Some(format!(
                        "{}: Entry[{}] Transaction[{}] - Failed to serialize reference transaction: {}",
                        filename, i, j, e
                    ))
                }
            };

            // Byte-by-byte comparison for 100% accuracy
            if custom_tx_bytes != ref_tx_bytes {
                return Some(format!(
                    "{}: Entry[{}] Transaction[{}] - Serialized bytes mismatch (Custom: {} bytes, Reference: {} bytes)",
                    filename,
                    i,
                    j,
                    custom_tx_bytes.len(),
                    ref_tx_bytes.len()
                ));
            }
        }
    }

    // If we get here, everything matches perfectly
    None
}

#[test]
fn test_decode_entries_on_real_world_data() {
    let data_dir = Path::new("tests/data");

    // Discover all shred files in data directory
    let mut shred_files = Vec::new();
    if let Ok(entries) = fs::read_dir(data_dir) {
        for entry in entries.flatten() {
            let path = entry.path();
            if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                if filename.starts_with("shred_") && filename.ends_with(".bin") {
                    shred_files.push(path);
                }
            }
        }
    }

    shred_files.sort();
    let total_files = shred_files.len();

    println!("🚀 Testing decode_entries on {} real-world shred samples...", total_files);
    println!("================================================================");

    let mut custom_success = 0;
    let mut reference_success = 0;
    let mut both_success = 0;
    let mut both_fail = 0;
    let mut custom_fail_ref_success = 0;
    let mut custom_success_ref_fail = 0;
    let mut accuracy_issues = Vec::new();

    for (index, file_path) in shred_files.iter().enumerate() {
        let filename = file_path.file_name().unwrap().to_str().unwrap();

        if let Ok(data) = fs::read(file_path) {
            // Test custom decoder
            let custom_result = decode_entries_internal(index as u64, &data);

            // Test official solana decoder
            let reference_result: Result<Vec<SolanaEntry>, _> = bincode::deserialize(&data);

            match (custom_result.is_ok(), reference_result.is_ok()) {
                (true, true) => {
                    both_success += 1;
                    custom_success += 1;
                    reference_success += 1;

                    // Deep accuracy check - compare actual data
                    let custom_entries = custom_result.unwrap();
                    let reference_entries = reference_result.unwrap();

                    if let Some(issue) = compare_entries_deeply(&custom_entries.entries, &reference_entries, filename) {
                        accuracy_issues.push(issue);
                    }
                }
                (true, false) => {
                    custom_success += 1;
                    custom_success_ref_fail += 1;
                }
                (false, true) => {
                    reference_success += 1;
                    custom_fail_ref_success += 1;
                    accuracy_issues.push(format!("{}: CRITICAL - Custom failed but reference succeeded", filename));
                }
                (false, false) => {
                    both_fail += 1;
                }
            }

            // Progress indicator
            if (index + 1) % 100 == 0 {
                println!("  Processed {} files...", index + 1);
            }
        }
    }

    // Calculate success rates
    let custom_success_rate = (custom_success as f64 / total_files as f64) * 100.0;
    let reference_success_rate = (reference_success as f64 / total_files as f64) * 100.0;
    let both_success_rate = (both_success as f64 / total_files as f64) * 100.0;

    // Print comprehensive report
    println!("\n🎯 REAL-WORLD DATA TEST RESULTS");
    println!("===============================");
    println!("Total files tested: {}", total_files);
    println!("Custom decoder success: {} ({:.2}%)", custom_success, custom_success_rate);
    println!("Reference decoder success: {} ({:.2}%)", reference_success, reference_success_rate);
    println!("Both succeeded: {} ({:.2}%)", both_success, both_success_rate);
    println!("Both failed: {}", both_fail);
    println!("Custom success, Reference fail: {}", custom_success_ref_fail);
    println!("Custom fail, Reference success: {} ⚠️", custom_fail_ref_success);

    if !accuracy_issues.is_empty() {
        println!("\n⚠️  ACCURACY ISSUES FOUND:");
        for issue in &accuracy_issues {
            println!("  {}", issue);
        }
    } else {
        println!("\n✅ No accuracy issues found!");
    }

    println!("\n🎯 ASSESSMENT:");
    if custom_success_rate >= 95.0 {
        println!("✅ EXCELLENT: Custom decoder success rate >= 95%");
    } else if custom_success_rate >= 90.0 {
        println!("✅ GOOD: Custom decoder success rate >= 90%");
    } else {
        println!("⚠️  NEEDS IMPROVEMENT: Custom decoder success rate < 90%");
    }

    if custom_fail_ref_success == 0 {
        println!("✅ PERFECT: No cases where custom failed but reference succeeded");
    } else {
        println!("❌ CRITICAL: {} cases where custom failed but reference succeeded", custom_fail_ref_success);
    }

    if accuracy_issues.is_empty() {
        println!("✅ ACCURATE: No entry count mismatches found");
    } else {
        println!("⚠️  ACCURACY: {} entry count mismatches found", accuracy_issues.len());
    }

    // Test assertions
    assert!(
        total_files >= 100,
        "Expected at least 100 test files, found {}. Run 'make collect-shreds' first.",
        total_files
    );

    assert!(
        custom_success_rate >= 90.0,
        "Custom decoder success rate {:.2}% is below 90% threshold",
        custom_success_rate
    );

    assert_eq!(
        custom_fail_ref_success, 0,
        "Found {} critical cases where custom failed but reference succeeded",
        custom_fail_ref_success
    );

    println!("\n🎉 REAL-WORLD DATA TEST PASSED!");
    println!("Custom decoder successfully handles real Solana network data!");
}

#[test]
fn test_decode_entries_sample_accuracy() {
    println!("🔬 Testing decode accuracy on sample files...");

    let data_dir = Path::new("tests/data");

    // Discover all shred files
    let mut shred_files = Vec::new();
    if let Ok(entries) = fs::read_dir(data_dir) {
        for entry in entries.flatten() {
            let path = entry.path();
            if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                if filename.starts_with("shred_") && filename.ends_with(".bin") {
                    shred_files.push(path);
                }
            }
        }
    }

    shred_files.sort();
    let mut tested = 0;

    // Test on first 10 successful files for detailed accuracy
    for file_path in shred_files.iter() {
        if tested >= 10 {
            break;
        }

        let filename = file_path.file_name().unwrap().to_str().unwrap();

        if let Ok(data) = fs::read(file_path) {
            let custom_result = decode_entries_internal(tested as u64, &data);
            let reference_result: Result<Vec<SolanaEntry>, _> = bincode::deserialize(&data);

            if let (Ok(custom), Ok(reference)) = (custom_result, reference_result) {
                tested += 1;

                // Use the same deep comparison function for 100% accuracy
                if let Some(issue) = compare_entries_deeply(&custom.entries, &reference, filename) {
                    panic!("Deep accuracy test failed: {}", issue);
                }

                println!("  ✅ {} - {} entries verified with 100% accuracy", filename, custom.entries.len());
            }
        }
    }

    assert!(tested >= 5, "Could not find enough valid samples for accuracy testing");
    println!("🎉 Sample accuracy test passed on {} files with 100% data accuracy!", tested);
}
