use shredstream_decoder::decode_entries_internal;
use solana_entry::entry::Entry as SolanaEntry;
use std::fs;
use std::path::Path;

#[test]
fn test_decode_entries_on_real_world_data() {
    println!("🚀 Testing decode_entries on 1,000 real-world shred samples...");
    println!("================================================================");
    
    let data_dir = Path::new("tests/data");
    let mut total_files = 0;
    let mut custom_success = 0;
    let mut reference_success = 0;
    let mut both_success = 0;
    let mut both_fail = 0;
    let mut custom_fail_ref_success = 0;
    let mut custom_success_ref_fail = 0;
    let mut accuracy_issues = Vec::new();
    
    // Test all 1,000 shred files
    for i in 1..=1000 {
        let filename = format!("shred_{:06}.bin", i);
        let file_path = data_dir.join(&filename);
        
        if let Ok(data) = fs::read(&file_path) {
            total_files += 1;
            
            // Test custom decoder
            let custom_result = decode_entries_internal(i as u64, &data);
            
            // Test official solana decoder
            let reference_result: Result<Vec<SolanaEntry>, _> = bincode::deserialize(&data);
            
            match (custom_result.is_ok(), reference_result.is_ok()) {
                (true, true) => {
                    both_success += 1;
                    custom_success += 1;
                    reference_success += 1;
                    
                    // Quick accuracy check - compare entry count
                    let custom_entries = custom_result.unwrap();
                    let reference_entries = reference_result.unwrap();
                    
                    if custom_entries.entries.len() != reference_entries.len() {
                        accuracy_issues.push(format!(
                            "{}: Entry count mismatch - Custom: {}, Reference: {}",
                            filename, custom_entries.entries.len(), reference_entries.len()
                        ));
                    }
                }
                (true, false) => {
                    custom_success += 1;
                    custom_success_ref_fail += 1;
                }
                (false, true) => {
                    reference_success += 1;
                    custom_fail_ref_success += 1;
                    accuracy_issues.push(format!(
                        "{}: CRITICAL - Custom failed but reference succeeded",
                        filename
                    ));
                }
                (false, false) => {
                    both_fail += 1;
                }
            }
            
            // Progress indicator
            if total_files % 100 == 0 {
                println!("  Processed {} files...", total_files);
            }
        }
    }
    
    // Calculate success rates
    let custom_success_rate = (custom_success as f64 / total_files as f64) * 100.0;
    let reference_success_rate = (reference_success as f64 / total_files as f64) * 100.0;
    let both_success_rate = (both_success as f64 / total_files as f64) * 100.0;
    
    // Print comprehensive report
    println!("\n🎯 REAL-WORLD DATA TEST RESULTS");
    println!("===============================");
    println!("Total files tested: {}", total_files);
    println!("Custom decoder success: {} ({:.2}%)", custom_success, custom_success_rate);
    println!("Reference decoder success: {} ({:.2}%)", reference_success, reference_success_rate);
    println!("Both succeeded: {} ({:.2}%)", both_success, both_success_rate);
    println!("Both failed: {}", both_fail);
    println!("Custom success, Reference fail: {}", custom_success_ref_fail);
    println!("Custom fail, Reference success: {} ⚠️", custom_fail_ref_success);
    
    if !accuracy_issues.is_empty() {
        println!("\n⚠️  ACCURACY ISSUES FOUND:");
        for issue in &accuracy_issues {
            println!("  {}", issue);
        }
    } else {
        println!("\n✅ No accuracy issues found!");
    }
    
    println!("\n🎯 ASSESSMENT:");
    if custom_success_rate >= 95.0 {
        println!("✅ EXCELLENT: Custom decoder success rate >= 95%");
    } else if custom_success_rate >= 90.0 {
        println!("✅ GOOD: Custom decoder success rate >= 90%");
    } else {
        println!("⚠️  NEEDS IMPROVEMENT: Custom decoder success rate < 90%");
    }
    
    if custom_fail_ref_success == 0 {
        println!("✅ PERFECT: No cases where custom failed but reference succeeded");
    } else {
        println!("❌ CRITICAL: {} cases where custom failed but reference succeeded", custom_fail_ref_success);
    }
    
    if accuracy_issues.is_empty() {
        println!("✅ ACCURATE: No entry count mismatches found");
    } else {
        println!("⚠️  ACCURACY: {} entry count mismatches found", accuracy_issues.len());
    }
    
    // Test assertions
    assert!(
        total_files >= 1000,
        "Expected at least 1000 test files, found {}. Run 'make collect-shreds' first.",
        total_files
    );
    
    assert!(
        custom_success_rate >= 90.0,
        "Custom decoder success rate {:.2}% is below 90% threshold",
        custom_success_rate
    );
    
    assert_eq!(
        custom_fail_ref_success, 0,
        "Found {} critical cases where custom failed but reference succeeded",
        custom_fail_ref_success
    );
    
    println!("\n🎉 REAL-WORLD DATA TEST PASSED!");
    println!("Custom decoder successfully handles real Solana network data!");
}

#[test]
fn test_decode_entries_sample_accuracy() {
    println!("🔬 Testing decode accuracy on sample files...");
    
    // Test on first 10 successful files for detailed accuracy
    let data_dir = Path::new("tests/data");
    let mut tested = 0;
    
    for i in 1..=1000 {
        if tested >= 10 { break; }
        
        let filename = format!("shred_{:06}.bin", i);
        let file_path = data_dir.join(&filename);
        
        if let Ok(data) = fs::read(&file_path) {
            let custom_result = decode_entries_internal(i as u64, &data);
            let reference_result: Result<Vec<SolanaEntry>, _> = bincode::deserialize(&data);
            
            if let (Ok(custom), Ok(reference)) = (custom_result, reference_result) {
                tested += 1;
                
                // Detailed comparison
                assert_eq!(custom.entries.len(), reference.len(), 
                    "Entry count mismatch in {}", filename);
                
                for (j, (custom_entry, ref_entry)) in custom.entries.iter().zip(reference.iter()).enumerate() {
                    assert_eq!(custom_entry.num_hashes, ref_entry.num_hashes,
                        "num_hashes mismatch in {} entry {}", filename, j);
                    
                    assert_eq!(custom_entry.hash.to_bytes(), ref_entry.hash.to_bytes(),
                        "hash mismatch in {} entry {}", filename, j);
                    
                    assert_eq!(custom_entry.transactions.len(), ref_entry.transactions.len(),
                        "transaction count mismatch in {} entry {}", filename, j);
                }
                
                println!("  ✅ {} - {} entries verified", filename, custom.entries.len());
            }
        }
    }
    
    assert!(tested >= 5, "Could not find enough valid samples for accuracy testing");
    println!("🎉 Sample accuracy test passed on {} files!", tested);
}
