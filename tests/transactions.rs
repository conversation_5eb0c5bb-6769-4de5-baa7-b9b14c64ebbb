mod common;

use shredstream_decoder::types::VersionedTransaction;

use common::{
    assertions::*, compatibility_validators::validators::*, fixtures::*, mock_data::generators::*,
    reference_implementations::reference::*,
};

mod versioned_transaction_tests {
    use super::*;
    use shredstream_decoder::types::{
        CompiledInstruction, LegacyMessage, MessageAddressTableLookup, MessageHeader, Pubkey, V0Message,
        VersionedMessage,
    };
    use solana_hash::Hash;
    use solana_message::{Message as SolanaMessage, VersionedMessage as SolanaVersionedMessage};
    use solana_signature::Signature;
    use solana_transaction::versioned::VersionedTransaction as SolanaVersionedTransaction;

    #[test]
    fn test_versioned_transaction_basic_structure() {
        let transaction = VersionedTransaction {
            signatures: vec![Signature::default()],
            message: VersionedMessage::Legacy(LegacyMessage::default()),
        };

        assert_eq!(transaction.signatures.len(), 1);
        assert!(matches!(transaction.message, VersionedMessage::Legacy(_)));
    }

    #[test]
    fn test_versioned_transaction_default() {
        let transaction = VersionedTransaction::default();

        assert!(transaction.signatures.is_empty());
        assert!(matches!(transaction.message, VersionedMessage::Legacy(_)));
    }

    #[test]
    fn test_versioned_transaction_clone_and_equality() {
        let signature = Signature::from([42u8; 64]);
        let message = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![Pubkey::new_from_array([1u8; 32])],
            recent_blockhash: Hash::new_from_array([2u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![0], data: vec![1, 2, 3] }],
        });

        let transaction1 = VersionedTransaction { signatures: vec![signature], message: message.clone() };
        let transaction2 = transaction1.clone();

        assert_eq!(transaction1, transaction2);
        assert_eq!(transaction1.signatures, transaction2.signatures);
        assert_eq!(transaction1.message, transaction2.message);
    }

    #[test]
    fn test_versioned_transaction_serialization_roundtrip() {
        let signature = Signature::from([123u8; 64]);
        let transaction = VersionedTransaction {
            signatures: vec![signature],
            message: VersionedMessage::V0(V0Message {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 2,
                },
                account_keys: vec![Pubkey::new_from_array([10u8; 32]), Pubkey::new_from_array([20u8; 32])],
                recent_blockhash: Hash::new_from_array([30u8; 32]),
                instructions: vec![CompiledInstruction {
                    program_id_index: 1,
                    accounts: vec![0, 1],
                    data: vec![100, 200],
                }],
                address_table_lookups: vec![MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([40u8; 32]),
                    writable_indexes: vec![0],
                    readonly_indexes: vec![1, 2],
                }],
            }),
        };

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(transaction, deserialized);
        assert_eq!(transaction.signatures, deserialized.signatures);
        assert_eq!(transaction.message, deserialized.message);
    }

    #[test]
    fn test_versioned_transaction_empty_signatures() {
        let transaction =
            VersionedTransaction { signatures: vec![], message: VersionedMessage::Legacy(LegacyMessage::default()) };

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(transaction, deserialized);
        assert!(transaction.signatures.is_empty());
        assert!(deserialized.signatures.is_empty());
    }

    #[test]
    fn test_versioned_transaction_multiple_signatures() {
        let signatures = vec![Signature::from([1u8; 64]), Signature::from([2u8; 64]), Signature::from([3u8; 64])];

        let transaction = VersionedTransaction {
            signatures: signatures.clone(),
            message: VersionedMessage::Legacy(LegacyMessage::default()),
        };

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(transaction, deserialized);
        assert_eq!(transaction.signatures.len(), 3);
        assert_eq!(deserialized.signatures.len(), 3);
        assert_eq!(transaction.signatures, signatures);
        assert_eq!(deserialized.signatures, signatures);
    }

    #[test]
    fn test_versioned_transaction_with_mock_generators() {
        let transaction = generate_mock_versioned_transaction();

        assert!(!transaction.signatures.is_empty());
        assert!(matches!(transaction.message, VersionedMessage::Legacy(_)));

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();
        assert_eq!(transaction, deserialized);
    }

    #[test]
    fn test_versioned_transaction_with_seed_generators() {
        let transaction1 = generate_mock_versioned_transaction_with_index(100);
        let transaction2 = generate_mock_versioned_transaction_with_index(100);
        let transaction3 = generate_mock_versioned_transaction_with_index(200);

        // Same seed should produce same result
        assert_eq!(transaction1, transaction2);
        // Different seed should produce different result
        assert_ne!(transaction1, transaction3);

        // All should serialize/deserialize correctly
        for tx in [&transaction1, &transaction2, &transaction3] {
            let serialized = bincode::serialize(tx).unwrap();
            let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();
            assert_eq!(*tx, deserialized);
        }
    }

    #[test]
    fn test_versioned_transaction_signatures_compatibility() {
        // Test with various signature scenarios
        let scenarios = vec![
            vec![],                                                                                        // Empty signatures
            vec![Signature::default()],         // Single default signature
            vec![Signature::from([255u8; 64])], // Single max signature
            vec![Signature::from([1u8; 64]), Signature::from([2u8; 64])], // Multiple signatures
            vec![Signature::from([100u8; 64]), Signature::from([200u8; 64]), Signature::from([50u8; 64])], // Three signatures
        ];

        for signatures in scenarios {
            let transaction = VersionedTransaction {
                signatures: signatures.clone(),
                message: VersionedMessage::Legacy(LegacyMessage::default()),
            };

            let serialized = bincode::serialize(&transaction).unwrap();
            let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

            assert_eq!(transaction.signatures, signatures);
            assert_eq!(deserialized.signatures, signatures);
            assert_eq!(transaction, deserialized);
        }
    }

    #[test]
    fn test_versioned_transaction_message_compatibility() {
        // Test with both Legacy and V0 message types
        let legacy_message = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 2,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 3,
            },
            account_keys: vec![
                Pubkey::new_from_array([10u8; 32]),
                Pubkey::new_from_array([20u8; 32]),
                Pubkey::new_from_array([30u8; 32]),
            ],
            recent_blockhash: Hash::new_from_array([40u8; 32]),
            instructions: vec![
                CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![100, 200, 255] },
                CompiledInstruction { program_id_index: 1, accounts: vec![0, 2], data: vec![150, 250] },
            ],
        });

        let v0_message = VersionedMessage::V0(V0Message {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 2,
            },
            account_keys: vec![Pubkey::new_from_array([50u8; 32]), Pubkey::new_from_array([60u8; 32])],
            recent_blockhash: Hash::new_from_array([70u8; 32]),
            instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![80, 90] }],
            address_table_lookups: vec![
                MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([100u8; 32]),
                    writable_indexes: vec![0, 1],
                    readonly_indexes: vec![2],
                },
                MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([110u8; 32]),
                    writable_indexes: vec![],
                    readonly_indexes: vec![0, 1, 2, 3],
                },
            ],
        });

        let messages = vec![legacy_message, v0_message];

        for message in messages {
            let transaction =
                VersionedTransaction { signatures: vec![Signature::from([123u8; 64])], message: message.clone() };

            let serialized = bincode::serialize(&transaction).unwrap();
            let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

            assert_eq!(transaction.message, message);
            assert_eq!(deserialized.message, message);
            assert_eq!(transaction, deserialized);
        }
    }

    #[test]
    fn test_versioned_transaction_legacy_message_in_transaction() {
        let legacy_message = VersionedMessage::Legacy(LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![Pubkey::new_from_array([42u8; 32])],
            recent_blockhash: Hash::new_from_array([84u8; 32]),
            instructions: vec![CompiledInstruction {
                program_id_index: 0,
                accounts: vec![],
                data: vec![1, 2, 3, 4, 5],
            }],
        });

        let transaction =
            VersionedTransaction { signatures: vec![Signature::from([200u8; 64])], message: legacy_message.clone() };

        assert!(matches!(transaction.message, VersionedMessage::Legacy(_)));

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(transaction, deserialized);
        assert!(matches!(deserialized.message, VersionedMessage::Legacy(_)));
    }

    #[test]
    fn test_versioned_transaction_v0_message_in_transaction() {
        let v0_message = VersionedMessage::V0(V0Message {
            header: MessageHeader {
                num_required_signatures: 2,
                num_readonly_signed_accounts: 1,
                num_readonly_unsigned_accounts: 0,
            },
            account_keys: vec![Pubkey::new_from_array([11u8; 32]), Pubkey::new_from_array([22u8; 32])],
            recent_blockhash: Hash::new_from_array([33u8; 32]),
            instructions: vec![
                CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![44, 55] },
                CompiledInstruction { program_id_index: 1, accounts: vec![0], data: vec![66, 77, 88] },
            ],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: Pubkey::new_from_array([99u8; 32]),
                writable_indexes: vec![0, 1, 2],
                readonly_indexes: vec![3, 4],
            }],
        });

        let transaction = VersionedTransaction {
            signatures: vec![Signature::from([111u8; 64]), Signature::from([222u8; 64])],
            message: v0_message.clone(),
        };

        assert!(matches!(transaction.message, VersionedMessage::V0(_)));
        assert_eq!(transaction.signatures.len(), 2);

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(transaction, deserialized);
        assert!(matches!(deserialized.message, VersionedMessage::V0(_)));
        assert_eq!(deserialized.signatures.len(), 2);
    }

    #[test]
    fn test_versioned_transaction_edge_cases() {
        // Test with maximum signatures (reasonable limit for testing)
        let max_signatures: Vec<Signature> = (0..10).map(|i| Signature::from([i as u8; 64])).collect();

        let transaction = VersionedTransaction {
            signatures: max_signatures.clone(),
            message: VersionedMessage::Legacy(LegacyMessage::default()),
        };

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(transaction, deserialized);
        assert_eq!(transaction.signatures.len(), 10);
        assert_eq!(deserialized.signatures.len(), 10);
        assert_eq!(transaction.signatures, max_signatures);

        // Test with zero signatures and complex V0 message
        let complex_v0_message = VersionedMessage::V0(V0Message {
            header: MessageHeader {
                num_required_signatures: 0,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 5,
            },
            account_keys: (0..5).map(|i| Pubkey::new_from_array([i as u8; 32])).collect(),
            recent_blockhash: Hash::new_from_array([255u8; 32]),
            instructions: (0..3)
                .map(|i| CompiledInstruction {
                    program_id_index: i,
                    accounts: vec![i, i + 1],
                    data: vec![i as u8, (i + 1) as u8, (i + 2) as u8],
                })
                .collect(),
            address_table_lookups: (0..2)
                .map(|i| MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([(i + 10) as u8; 32]),
                    writable_indexes: vec![i as u8, (i + 1) as u8],
                    readonly_indexes: vec![(i + 2) as u8, (i + 3) as u8, (i + 4) as u8],
                })
                .collect(),
        });

        let zero_sig_transaction = VersionedTransaction { signatures: vec![], message: complex_v0_message };

        let serialized = bincode::serialize(&zero_sig_transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(zero_sig_transaction, deserialized);
        assert!(zero_sig_transaction.signatures.is_empty());
        assert!(deserialized.signatures.is_empty());
    }

    #[test]
    fn test_versioned_transaction_large_arrays() {
        // Test with large signature array
        let large_signatures: Vec<Signature> = (0..50)
            .map(|i| {
                let mut bytes = [0u8; 64];
                bytes[0] = i as u8;
                bytes[63] = (255 - i) as u8;
                Signature::from(bytes)
            })
            .collect();

        let transaction = VersionedTransaction {
            signatures: large_signatures.clone(),
            message: VersionedMessage::Legacy(LegacyMessage {
                header: MessageHeader {
                    num_required_signatures: 50,
                    num_readonly_signed_accounts: 10,
                    num_readonly_unsigned_accounts: 20,
                },
                account_keys: (0..30).map(|i| Pubkey::new_from_array([i as u8; 32])).collect(),
                recent_blockhash: Hash::new_from_array([200u8; 32]),
                instructions: (0..20)
                    .map(|i| CompiledInstruction {
                        program_id_index: (i % 30) as u8,
                        accounts: (0..5).map(|j| ((i + j) % 30) as u8).collect(),
                        data: (0..10).map(|k| ((i + k) % 256) as u8).collect(),
                    })
                    .collect(),
            }),
        };

        let serialized = bincode::serialize(&transaction).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        assert_eq!(transaction, deserialized);
        assert_eq!(transaction.signatures.len(), 50);
        assert_eq!(deserialized.signatures.len(), 50);
        assert_eq!(transaction.signatures, large_signatures);
        assert_eq!(deserialized.signatures, large_signatures);
    }

    #[test]
    fn test_versioned_transaction_comprehensive_roundtrip() {
        // Test comprehensive scenarios with different combinations
        let test_cases = vec![
            // Case 1: Empty signatures with Legacy message
            (vec![], VersionedMessage::Legacy(LegacyMessage::default())),
            // Case 2: Single signature with V0 message
            (vec![Signature::from([42u8; 64])], VersionedMessage::V0(V0Message::default())),
            // Case 3: Multiple signatures with complex Legacy message
            (
                vec![Signature::from([1u8; 64]), Signature::from([2u8; 64]), Signature::from([3u8; 64])],
                VersionedMessage::Legacy(LegacyMessage {
                    header: MessageHeader {
                        num_required_signatures: 3,
                        num_readonly_signed_accounts: 1,
                        num_readonly_unsigned_accounts: 2,
                    },
                    account_keys: vec![
                        Pubkey::new_from_array([10u8; 32]),
                        Pubkey::new_from_array([20u8; 32]),
                        Pubkey::new_from_array([30u8; 32]),
                    ],
                    recent_blockhash: Hash::new_from_array([40u8; 32]),
                    instructions: vec![CompiledInstruction {
                        program_id_index: 0,
                        accounts: vec![1, 2],
                        data: vec![100, 200],
                    }],
                }),
            ),
            // Case 4: Multiple signatures with complex V0 message
            (
                vec![Signature::from([100u8; 64]), Signature::from([200u8; 64])],
                VersionedMessage::V0(V0Message {
                    header: MessageHeader {
                        num_required_signatures: 2,
                        num_readonly_signed_accounts: 0,
                        num_readonly_unsigned_accounts: 3,
                    },
                    account_keys: vec![
                        Pubkey::new_from_array([50u8; 32]),
                        Pubkey::new_from_array([60u8; 32]),
                        Pubkey::new_from_array([70u8; 32]),
                    ],
                    recent_blockhash: Hash::new_from_array([80u8; 32]),
                    instructions: vec![
                        CompiledInstruction { program_id_index: 0, accounts: vec![1, 2], data: vec![90, 95] },
                        CompiledInstruction { program_id_index: 1, accounts: vec![0, 2], data: vec![110, 120, 130] },
                    ],
                    address_table_lookups: vec![MessageAddressTableLookup {
                        account_key: Pubkey::new_from_array([140u8; 32]),
                        writable_indexes: vec![0, 1],
                        readonly_indexes: vec![2, 3],
                    }],
                }),
            ),
        ];

        for (i, (signatures, message)) in test_cases.into_iter().enumerate() {
            let transaction = VersionedTransaction { signatures: signatures.clone(), message: message.clone() };

            let serialized = bincode::serialize(&transaction).unwrap();
            let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

            assert_eq!(transaction, deserialized, "Test case {} failed", i);
            assert_eq!(transaction.signatures, signatures, "Signatures mismatch in test case {}", i);
            assert_eq!(transaction.message, message, "Message mismatch in test case {}", i);
            assert_eq!(deserialized.signatures, signatures, "Deserialized signatures mismatch in test case {}", i);
            assert_eq!(deserialized.message, message, "Deserialized message mismatch in test case {}", i);
        }
    }
}
